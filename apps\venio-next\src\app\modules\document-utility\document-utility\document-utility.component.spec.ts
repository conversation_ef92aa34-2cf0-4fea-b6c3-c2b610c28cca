import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentUtilityComponent } from './document-utility.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import {
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  ReviewFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { VenioNotificationService } from '@venio/feature/notification'
import { BehaviorSubject } from 'rxjs'

describe('DocumentUtilityComponent', () => {
  let component: DocumentUtilityComponent
  let fixture: ComponentFixture<DocumentUtilityComponent>
  let queryParamsSubject: BehaviorSubject<any>

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })

    await TestBed.configureTestingModule({
      imports: [
        DocumentUtilityComponent,
        NoopAnimationsModule,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
        SearchResultFacade,
        SearchFacade,
        FieldFacade,
        StartupsFacade,
        DocumentsFacade,
        NotificationService,
        ReviewFacade,
        CaseInfoFacade,
        VenioNotificationService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentUtilityComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
