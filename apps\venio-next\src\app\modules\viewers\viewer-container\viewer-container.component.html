<ng-container>
  <kendo-tabstrip
    #tabstrip
    (tabSelect)="onTabSelect($event)"
    [keepTabContent]="true"
    class="t-w-full t-h-full t-pl-3">
    <kendo-tabstrip-tab
      title="Native"
      [selected]="true"
      [cssClass]="'t-overflow-y-hidden'"
      *ngIf="
        hasViewerPanelInLayout(ReviewPanelType.NearNativeViewer) &&
        hasNativeViewerRight &&
        (currentFileId ?? 0) > 0
      ">
      <ng-template kendoTabContent>
        <div class="t-overflow-y-hidden h-99">
          <ng-container
            *ngComponentOutlet="
              nearNativeViewerComponent | async
            "></ng-container>
        </div>
      </ng-template>
    </kendo-tabstrip-tab>

    <kendo-tabstrip-tab
      title="Fulltext"
      *ngIf="
        hasViewerPanelInLayout(ReviewPanelType.TextViewer) &&
        hasFulltextViewerRight &&
        (currentFileId ?? 0) > 0
      ">
      <ng-template kendoTabContent>
        <ng-container
          *ngComponentOutlet="fulltextViewerComponent | async"></ng-container>
      </ng-template>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab
      title="PDF"
      *ngIf="
        isImageTypePdf &&
        hasImageViewerRight &&
        hasViewerPanelInLayout(ReviewPanelType.ImageViewer) &&
        (currentFileId ?? 0) > 0
      ">
      <ng-container
        *venioHasUserGroupRights="UserRights.ALLOW_TO_VIEW_TIFF_VIEWER">
        <ng-template kendoTabContent #pdfViewer>
          <div class="content">
            <ng-container
              *ngComponentOutlet="pdfViewerComponent | async"></ng-container>
          </div>
        </ng-template>
      </ng-container>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab
      title="Tiff"
      *ngIf="
        !isImageTypePdf &&
        hasImageViewerRight &&
        hasViewerPanelInLayout(ReviewPanelType.ImageViewer) &&
        (currentFileId ?? 0) > 0
      ">
      <ng-container
        *venioHasUserGroupRights="UserRights.ALLOW_TO_VIEW_TIFF_VIEWER">
        <ng-template kendoTabContent #tiffViewer>
          <div class="content">
            <ng-container
              *ngComponentOutlet="tiffViewerComponent | async"></ng-container>
          </div>
        </ng-template>
      </ng-container>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab
      title="Transcript"
      *ngIf="
        isTranscriptViewerEnabled &&
        hasTranscriptViewerRight &&
        (isTranscriptDefaultViewer || (currentFileId ?? 0) > 0)
      ">
      <ng-container
        *venioHasUserGroupRights="UserRights.ALLOW_TO_VIEW_TRANSCRIPT">
        <ng-template kendoTabContent #transcriptViewer>
          <div class="content t-h-full t-w-full">
            <ng-container
              *ngComponentOutlet="
                transcriptViewerComponent | async
              "></ng-container>
          </div>
        </ng-template>
      </ng-container>
    </kendo-tabstrip-tab>
  </kendo-tabstrip>
</ng-container>
