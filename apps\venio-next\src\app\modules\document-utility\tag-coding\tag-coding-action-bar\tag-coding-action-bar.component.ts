import { CommonModule } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  Inject,
  OnDestroy,
  OnInit,
  signal,
  Type as Types,
  ViewChild,
  ViewContainerRef,
  inject,
} from '@angular/core'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { LayoutModule } from '@progress/kendo-angular-layout'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
  ReviewPanelFacade,
  ReviewPanelViewState,
} from '@venio/data-access/document-utility'
import {
  AfterValueChangedDirective,
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import {
  DocumentActionTypeTitle,
  DocumentMenuType,
  PageControlActionType,
  ShortcutKeyActions,
  ShortcutKeyBindings,
} from '@venio/shared/models/constants'
import {
  combineLatest,
  distinctUntilChanged,
  filter,
  Subject,
  Subscription,
  timer,
  takeUntil,
} from 'rxjs'
import {
  DocumentsFacade,
  ReviewsetFacade,
  ReviewSetStateService,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import {
  NotificationModule,
  Type,
  NotificationRef,
  NotificationService,
} from '@progress/kendo-angular-notification'
import { DialogModule, DialogService } from '@progress/kendo-angular-dialog'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { DocumentTagUtilityService } from '../../utility-services/document-tag-utility'
import { ActivatedRoute } from '@angular/router'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import {
  AppIdentitiesTypes,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import { LocalStorage } from '@venio/shared/storage'
import { environment } from '@venio/shared/environments'
import { NotificationTriggerService } from '../../../../services/shared-custom.notification.service'
import { ReactiveFormsModule, FormsModule, FormControl } from '@angular/forms'
import { Callback, ShortcutManager } from '@venio/util/utilities'
import { ConfirmationDialogService } from '../../../../services/confirmation-dialog-service'
import { infoCircleIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-tag-coding-action-bar',
  templateUrl: './tag-coding-action-bar.component.html',
  styleUrls: ['./tag-coding-action-bar.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SvgLoaderDirective,
    ReactiveFormsModule,
    FormsModule,
    ButtonsModule,
    InputsModule,
    LayoutModule,
    LabelModule,
    NotificationModule,
    DialogModule,
    AfterValueChangedDirective,
    TooltipsModule,
    IndicatorsModule,
    UserGroupRightCheckDirective,
    IconsModule,
  ],
  providers: [DocumentTagUtilityService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagCodingActionBarComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private reviewSetState = inject(ReviewSetStateService)

  private reviewSetFacade = inject(ReviewsetFacade)

  private documentFacade = inject(DocumentsFacade)

  private currentFileId = toSignal(this.documentFacade.getCurrentDocument$)

  /**
   * Container placeholder for EDIT action of a gear icon menu
   */
  public documentEditContainer = import(
    '../../edit-action/document-edit-container/document-edit-container.component'
  ).then((m) => m.DocumentEditContainerComponent)

  public searchTagControl: FormControl = new FormControl()

  public infoCircleIcon: SVGIcon = infoCircleIcon

  public showTagCodingFilter = false

  public filterTerm = ''

  public readonly canViewCodingPanel = toSignal(
    this.startupsFacade.hasGroupRight$(UserRights.ALLOW_TO_VIEW_CODING_PANE),
    {
      initialValue: false,
    }
  )

  public isTagCommentValidationRequired = false

  public readonly isCodingDataValid = signal(false)

  public isTagAndCodingFieldUpdate = signal(false)

  public readonly tagGroupName = this.reviewPanelViewState.tagGroupName

  public readonly isAIGroup = computed(
    () => !this.reviewPanelViewState.tagGroupName().includes('User')
  )

  public tagCommentValidationMessage =
    'Please enter comments for required tags.'

  private unsubscribed$ = new Subject<void>()

  public UserRights = UserRights

  private shortcutManager: ShortcutManager

  public DocumentActionTypeTitleInstance = DocumentActionTypeTitle

  public isTagRuleListLoaded$ =
    this.documentTagFacade.selectIsTagRuleListLoaded$

  public tagRuleInfoComp: Promise<Types<unknown>>

  public notificationRef: NotificationRef

  private autoHideSubscription: Subscription | null = null

  public svgIconForTagControls = [
    {
      actionType: PageControlActionType.PREV_PAGE,
      actionText:
        this.reviewSetState.isBatchReview() &&
        this.reviewSetState.reviewSetBasicInfo()?.markTaggedDocsAsReviewed
          ? DocumentActionTypeTitle.REVIEW_MOVE_PREVIOUS_DOCUMENT_TEXT
          : DocumentActionTypeTitle.MOVE_PREVIOUS_DOCUMENT_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
      allowedPermission: UserRights.ALLOW_TAGGING_UNTAGGING,
      isFirst: true,
      isLast: false,
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      actionText:
        this.reviewSetState.isBatchReview() &&
        this.reviewSetState.reviewSetBasicInfo()?.markTaggedDocsAsReviewed
          ? DocumentActionTypeTitle.REVIEW_MOVE_NEXT_DOCUMENT_TEXT
          : DocumentActionTypeTitle.MOVE_NEXT_DOCUMENT_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
      allowedPermission: UserRights.ALLOW_TAGGING_UNTAGGING,
      isFirst: false,
      isLast: false,
    },
    {
      actionType: PageControlActionType.VIEW,
      actionText: DocumentActionTypeTitle.VIEW_TEXT,
      iconPath: 'assets/svg/icon-review-eye.svg',
      isFirst: false,
      isLast: false,
    },
    {
      actionType: PageControlActionType.SAVE,
      actionText: DocumentActionTypeTitle.SAVE_TEXT,
      iconPath: 'assets/svg/icon-review-save.svg',
      allowedPermission: UserRights.ALLOW_TAGGING_UNTAGGING,
      isFirst: false,
      isLast: false,
    },
    {
      actionType: PageControlActionType.ADD,
      actionText: DocumentActionTypeTitle.ADD_TEXT,
      iconPath: 'assets/svg/icon-review-plus.svg',
      isFirst: false,
      isLast: true,
    },
  ]

  @ViewChild('actionBar', { read: ViewContainerRef })
  public actionBar: ViewContainerRef

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  constructor(
    private documentTagFacade: DocumentTagFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private documentsFacade: DocumentsFacade,
    private documentTagUtilityService: DocumentTagUtilityService,
    private confirmationDialogService: ConfirmationDialogService,
    private startupsFacade: StartupsFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private reviewPanelViewState: ReviewPanelViewState,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private dialogService: DialogService,
    @Inject(WINDOW) private windowRef: Window,
    private vcr: ViewContainerRef,
    private notificationTriggerService: NotificationTriggerService,
    private notificationServiceKendo: NotificationService
  ) {}

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#selectFilterDocumentTags()
    this.#selectIsCodingDataValid()
    this.#selectTagCodingResponses()
    this.#fetchDocumentTagCommentValidation()
    this.#setIsFirstAsPerRight()
    this.#selectTagAndCodingChanges()
    this.#selectShortcutKeysAction()
  }

  public ngAfterViewInit(): void {
    this.#initTagCodingShortcutKeys()
    this.#loadLazyLoadComponents()
  }

  #loadLazyLoadComponents(): void {
    this.tagRuleInfoComp = import('@venio/feature/tag-email-thread').then(
      ({ TagRuleInfoContentComponent }) => TagRuleInfoContentComponent
    )
  }

  #selectFilterDocumentTags(): void {
    this.documentTagFacade.selectFilterDocumentTags$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribed$))
      .subscribe((search) => {
        this.searchTagControl.setValue(search, { emitEvent: false })
        this.changeDetectorRef.markForCheck()
      })
  }

  #setIsFirstAsPerRight(): void {
    this.startupsFacade
      .hasGroupRight$(UserRights.ALLOW_TAGGING_UNTAGGING)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((allowTag) => {
        const index = this.svgIconForTagControls.findIndex(
          (icon) => icon.actionType === PageControlActionType.VIEW
        )
        this.svgIconForTagControls[index].isFirst = !allowTag
      })
  }

  #selectTagCodingResponses(): void {
    combineLatest([
      this.documentTagFacade.applyDocumentTagSuccessResponse$,
      this.documentTagFacade.applyDocumentTagErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        if (success?.data.showTagCodingConfirmation)
          this.openTagCodingSummaryDialog(success.data)

        if (success) {
          this.#addCopyDocumentTags()
          this.#resetDocumentTagSuccessResponseState()
          this.#resetMultiCodingValue()
          this.#showMessage(success.message, {
            style: 'success',
          })
        } else {
          this.#resetDocumentTagErrorResponseState()
          if (error.message !== 'Tag rule violated') {
            this.showErrorNotification(error.message, {
              style: 'error',
            })
          }
        }

        this.changeDetectorRef.markForCheck()
      })
  }

  #selectTagAndCodingChanges(): void {
    combineLatest([
      this.documentTagFacade.selectIsTagDataModified$,
      this.documentCodingFacade.selectIsCodingDataModified$,
    ])
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe(([tag, coding]) => {
        const isDataModified = Boolean(tag || coding)
        this.isTagAndCodingFieldUpdate.set(isDataModified)
      })
  }

  public openTagCodingSummaryDialog(tagCodingSummary: any): void {
    import(
      '../tag-coding-summary-dialog/tag-coding-summary-dialog.component'
    ).then((td) => {
      const dialogRef = this.dialogService.open({
        content: td.TagCodingSummaryDialogComponent,
        maxWidth: '1600',
        minWidth: '250',
        width: '80%',
        height: '90vh',
      })

      const tagCodingSummaryDialog = dialogRef.content.instance
      tagCodingSummaryDialog.tagCodingDetails = tagCodingSummary
    })
  }

  #addCopyDocumentTags(): void {
    this.reviewPanelViewState.addCopyDocumentTags()
  }

  #resetDocumentTagSuccessResponseState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'applyDocumentTagSuccessResponse',
      'isTagDataModified',
    ])
  }

  #resetDocumentTagErrorResponseState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'applyDocumentTagErrorResponse',
    ])
  }

  #resetMultiCodingValue(): void {
    this.documentCodingFacade.resetDocumentCodingState([
      'updatedCodingFieldInfoIds',
      'isCodingDataModified',
    ])
  }

  #fetchDocumentTagCommentValidation(): void {
    this.documentTagFacade.areTagCommentsMissing$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isTagCommentValidationRequired) => {
        this.isTagCommentValidationRequired = isTagCommentValidationRequired
      })
  }

  #selectIsCodingDataValid(): void {
    this.documentCodingFacade.selectIsCodingDataValid$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isCodingDataValid) => {
        this.isCodingDataValid.set(isCodingDataValid)
      })
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    //custom notification service
    this.notificationTriggerService.triggerNotification(
      content,
      type,
      3500,
      300
    )
  }

  public showErrorNotification(content: string, type: Type): void {
    this.notificationRef?.hide()

    this.autoHideSubscription?.unsubscribe()

    this.notificationRef = this.notificationServiceKendo.show({
      content: content,
      appendTo: this.vcr,
      position: { horizontal: 'center', vertical: 'top' },
      animation: { type: 'fade', duration: 100 },
      closable: true,
      type: type,
      cssClass: 'v-rule-notification v-coding-error-notification',
    })

    // In kendo the hideAfter property is ignored when the closable property is set to true. To resolve this, we are using the timer to hide the notification after some delay.
    this.autoHideSubscription = timer(3500).subscribe(() => {
      this.notificationRef?.hide()
    })
  }

  #sendTagActionEventToPopoutWindow(actionType: PageControlActionType): void {
    const isActionForPopoutWindow =
      actionType === PageControlActionType.SAVE ||
      actionType === PageControlActionType.NEXT_PAGE ||
      actionType === PageControlActionType.PREV_PAGE

    // Popout window is not open then return
    if (!this.isTagPanelPopout) return

    // Popout window is open, and the action is not for popout window then return
    if (!isActionForPopoutWindow) return

    this.windowRef.opener.postMessage(
      {
        type: 'MICRO_APP_DATA_CHANGE',
        payload: {
          type: MessageType.WINDOW_CHANGE,
          content: {
            pageControlActionType: actionType,
          },
        },
        eventTriggeredBy: AppIdentitiesTypes.UTILITY_PANEL_ACTION,
        iframeIdentity: AppIdentitiesTypes.UTILITY_PANEL,
        eventTriggeredFor: 'ALL_WINDOW',
      },
      environment.allowedOrigin
    )
  }

  public browseActionClicked(actionType: PageControlActionType): void {
    switch (actionType) {
      case 'PREV_PAGE':
        if (
          this.reviewSetState.isBatchReview() &&
          this.reviewSetState.reviewSetBasicInfo()?.markTaggedDocsAsReviewed
        ) {
          const fileId = this.currentFileId()
          if (fileId > 0)
            this.reviewSetFacade.markCurrentDocumentAsReviewedAction.next(
              fileId
            )
        }
        this.saveTag(actionType)
        break
      case 'NEXT_PAGE':
        if (
          this.reviewSetState.isBatchReview() &&
          this.reviewSetState.reviewSetBasicInfo()?.markTaggedDocsAsReviewed
        ) {
          const fileId = this.currentFileId()
          if (fileId > 0)
            this.reviewSetFacade.markCurrentDocumentAsReviewedAction.next(
              fileId
            )
        }
        this.saveTag(actionType)
        break
      case 'VIEW':
        this.triggerPageActionType(actionType)
        break
      case 'SAVE':
        this.saveTag(actionType)
        break
      case 'ADD':
        this.triggerPageActionEdit()
        break
    }

    this.#sendTagActionEventToPopoutWindow(actionType)
  }

  public onFilter(value): void {
    const searchValue: string = value
    this.documentTagFacade.filterDocumentTags(searchValue)
  }

  private triggerPageActionType(actionType: PageControlActionType): void {
    this.documentTagFacade.triggerPageActionEvent(actionType)
  }

  private triggerPageActionEdit(): void {
    this.documentsFacade.triggerMenuEvent(DocumentMenuType.EDIT)
  }

  private saveTag(navigationType: PageControlActionType): void {
    if (this.isTagCommentValidationRequired) {
      this.#showMessage(this.tagCommentValidationMessage, {
        style: 'error',
      })
      return
    }

    this.documentTagUtilityService.saveTag(this.projectId, navigationType)
  }

  private isDataLoading(isLoading: boolean): void {
    this.documentTagFacade.setIsDocumentTagLoading(isLoading)
    this.documentCodingFacade.setIsDocumentCodingLoading(isLoading)
    this.changeDetectorRef.markForCheck()
  }

  #selectShortcutKeysAction(): void {
    this.reviewPanelFacade.getShortcutKeysAction
      .pipe(
        filter((action) => action === ShortcutKeyActions.SAVE_TAG_CODING),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.saveTag(PageControlActionType.NEXT_PAGE)
      })
  }

  #showTagCodingConfirmationMessage(): void {
    const message = this.isTagAndCodingFieldUpdate()
      ? 'Are you sure you want to undo? All the changes will be reverted back to the previous values.'
      : 'Saved actions will not be reverted back'

    this.confirmationDialogService
      .showDialogByType(
        this.isTagAndCodingFieldUpdate(),
        'Undo',
        message,
        this.vcr
      )
      .pipe(
        filter((confirmed) => confirmed),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.reviewPanelFacade.setShortcutKeysAction =
          ShortcutKeyActions.UNDO_TAG_CODING
      })
  }

  private tagCodingShortcutHandler: Callback = (event, combo): void => {
    const actions: { [key: string]: () => void } = {
      [ShortcutKeyBindings.SAVE_TAG_CODING]: () => {
        this.reviewPanelFacade.setShortcutKeysAction =
          ShortcutKeyActions.SAVE_TAG_CODING
      },
      [ShortcutKeyBindings.UNDO_TAG_CODING]: () => {
        this.#showTagCodingConfirmationMessage()
      },
    }

    // Check if the combo is one of the defined shortcuts
    if (actions[combo]) {
      event.preventDefault()
      event.stopPropagation()
      actions[combo]() // Execute the corresponding action
    }
  }

  #initTagCodingShortcutKeys(): void {
    this.shortcutManager = new ShortcutManager()
    this.shortcutManager.bind(
      [
        ShortcutKeyBindings.SAVE_TAG_CODING,
        ShortcutKeyBindings.UNDO_TAG_CODING,
      ],
      this.tagCodingShortcutHandler
    )
  }

  #removeTagCodingShortcutKeys(): void {
    this.shortcutManager.unbind(
      [
        ShortcutKeyBindings.SAVE_TAG_CODING,
        ShortcutKeyBindings.UNDO_TAG_CODING,
      ],
      undefined,
      this.tagCodingShortcutHandler
    )
  }

  public showAllTagRules(): void {
    this.#resetDocumentTagRuleState()
  }

  #resetDocumentTagRuleState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'filterTagRuleId',
      'isTagHeaderRequired',
      'tagRuleViolationList',
      'showTagRuleList',
    ])
  }

  public ngOnDestroy(): void {
    this.#removeTagCodingShortcutKeys()
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    this.autoHideSubscription?.unsubscribe()
  }
}
