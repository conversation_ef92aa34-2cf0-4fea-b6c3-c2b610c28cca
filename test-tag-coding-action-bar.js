const { execSync } = require('child_process');

try {
  console.log('Running TagCodingActionBarComponent tests...');
  
  const result = execSync(
    'npx jest apps/venio-next/src/app/modules/document-utility/tag-coding/tag-coding-action-bar/tag-coding-action-bar.component.spec.ts --verbose --no-cache',
    { 
      stdio: 'inherit',
      cwd: process.cwd()
    }
  );
  
  console.log('Tests completed successfully!');
} catch (error) {
  console.error('Tests failed:', error.message);
  process.exit(1);
}
